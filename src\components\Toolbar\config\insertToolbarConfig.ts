import { CodeIcon, ImageIcon } from "@radix-ui/react-icons"

import { IconIcon } from "../../icons/IconIcon"
import { InfoIcon } from "../../icons/InfoIcon"
import { MessageIcon } from "../../icons/MessageIcon"
import { TaskIcon } from "../../icons/TaskIcon"
import {
  HyperlinkIcon,
  RootThemeIcon,
  SameLevelIcon,
  StyleBorderIcon,
  StyleLineIcon,
  SubThemeIcon,
  SummaryIcon,
  WatermarkIcon
} from "../icons"
import type { ToolbarItem } from "./startToolbarConfig"
import { TagIcon } from "../../icons/TagIcon"

// 插入工具栏配置
export const insertToolbarConfig: ToolbarItem[] = [
  // 节点样式组
  {
    type: "button",
    id: "subTheme",
    label: "子主题",
    icon: SubThemeIcon,
    text: "子主题",
    className: "toolbar-text-btn",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "sameTheme",
    label: "同级主题",
    icon: SameLevelIcon,
    text: "同级主题",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "rootTheme",
    label: "父主题",
    icon: RootThemeIcon,
    text: "父主题",
    disabled: "!isEnabled"
  },

  { type: "separator" },

  {
    type: "button",
    id: "lineType",
    label: "关联",
    icon: StyleLineIcon,
    text: "关联",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "summary",
    label: "概要",
    icon: SummaryIcon,
    text: "概要",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "border",
    label: "外框",
    icon: StyleBorderIcon,
    text: "外框",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "image",
    label: "图片",
    icon: ImageIcon,
    text: "图片",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "tag",
    label: "标签",
    icon: TagIcon,
    text: "标签",
    disabled: "!isEnabled"
  },

  {
    type: "button",
    id: "task",
    label: "任务",
    icon: TaskIcon,
    text: "任务",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "link",
    label: "超链接",
    icon: HyperlinkIcon,
    text: "超链接",
    disabled: "!isEnabled"
  },

  {
    type: "button",
    id: "info",
    label: "备注",
    icon: InfoIcon,
    text: "备注",
    disabled: "!isEnabled"
  },

  {
    type: "button",
    id: "message",
    label: "评论",
    icon: MessageIcon,
    text: "评论",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "watermark",
    label: "水印",
    icon: WatermarkIcon,
    text: "水印",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "structure",
    label: "模版",
    icon: TagIcon,
    text: "模版",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "structure",
    label: "Latex方程式",
    icon: TagIcon,
    text: "Latex方程式",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "icon",
    label: "图标",
    icon: IconIcon,
    text: "图标",
    disabled: "!isEnabled"
  },
  {
    type: "button",
    id: "code",
    label: "代码块",
    icon: CodeIcon,
    text: "代码块",
    disabled: "!isEnabled"
  }
]
