/* 头部导航样式 */
.header-nav {
  background: #eeeeee;
  padding: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 编辑组样式 */
.edit-controls {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.edit-btn {
  width: 50px;
  height: 24px;
  border: 1px solid transparent;
  background: transparent;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
  color: #444;
  transition: all 0.15s ease;
  font-size: 12px;
  padding: 2px 6px;
  gap: 4px;
}

.edit-btn span {
  font-size: 11px;
}

.edit-btn:hover {
  background-color: #e1ecf4;
  border-color: #bee5eb;
}

/* 思维导图样式 */
.mindmap-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #f8fafc;
}

.mindmap-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.mindmap-node {
  position: absolute;
  background: #0891b2;
  color: white;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  transform: translate(-50%, -50%);
  width: max-content; /* 让宽度完全由内容决定 */
  min-width: 60px;
  max-width: 800px; /* 设置更大的最大宽度，确保内容不会溢出 */
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all; /* 更激进的换行策略，在任何字符处换行 */
  overflow-wrap: anywhere; /* 最新的换行属性，允许在任何地方换行 */
  hyphens: auto; /* 启用连字符换行 */
  overflow: visible;
  height: auto;
  min-height: 40px;
  user-select: none;
  /* 使用flex布局，但确保能正确处理换行 */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.mindmap-node:hover {
  background: #0e7490;
  transform: translate(-50%, -50%) scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mindmap-node.selected {
  background: #059669;
  box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.3);
}

.mindmap-node.level-1 {
  background: #7c3aed;
  font-size: 13px;
  padding: 10px 16px;
  min-width: 80px;
  max-width: 600px; /* 设置更大的最大宽度 */
  min-height: 36px;
}

.mindmap-node.level-1:hover {
  background: #6d28d9;
}

.mindmap-node.level-1.selected {
  background: #7c2d12;
  box-shadow: 0 0 0 3px rgba(124, 45, 18, 0.3);
}

.mindmap-node.level-2 {
  background: #dc2626;
  font-size: 12px;
  padding: 8px 12px;
  min-width: 70px;
  max-width: 600px; /* 设置更大的最大宽度 */
  min-height: 32px;
}

.mindmap-node.level-2:hover {
  background: #b91c1c;
}

.mindmap-node.level-2.selected {
  background: #991b1b;
  box-shadow: 0 0 0 3px rgba(153, 27, 27, 0.3);
}

.mindmap-input {
  position: absolute;
  background: white;
  border: 2px solid #0891b2;
  border-radius: 20px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  transform: translate(-50%, -50%);
  width: auto;
  min-width: 60px;
  max-width: 800px; /* 与节点的最大宽度保持一致 */
  text-align: center;
  outline: none;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: visible;
  height: auto;
  min-height: 40px;
  resize: none;
}

.mindmap-line {
  stroke: #94a3b8;
  stroke-width: 2;
  fill: none;
}

/* 右键菜单样式 - Radix UI ContextMenu */
.context-menu {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 200px;
  z-index: 1000;
  font-size: 14px;
  /* Radix UI 特定样式 */
  animation-duration: 400ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
}

/* Radix UI ContextMenu 动画 */
.context-menu[data-state="open"] {
  animation-name: slideUpAndFade;
}

@keyframes slideUpAndFade {
  from {
    opacity: 0;
    transform: translateY(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.context-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  cursor: pointer;
  border: none;
  background: none;
  text-align: left;
  color: #374151;
  transition: background-color 0.15s ease;

  outline: none;
  user-select: none;
}

.context-menu-item:hover,
.context-menu-item[data-highlighted] {
  background-color: #f3f4f6;
}

.context-menu-item.disabled,
.context-menu-item[data-disabled] {
  color: #9ca3af;
  cursor: not-allowed;
  pointer-events: none;
}

.context-menu-item.disabled:hover,
.context-menu-item[data-disabled]:hover,
.context-menu-item[data-disabled][data-highlighted] {
  background-color: transparent;
}

.context-menu-item .icon {
  margin-right: 12px;
  width: 16px;
  height: 16px;
}

.context-menu-item .shortcut {
  color: #9ca3af;
  font-size: 12px;
}

.context-menu-separator {
  height: 1px;
  background: #e5e7eb;
  margin: 4px 0;
}

/* AI功能相关样式 */
.context-menu-item svg {
  width: 16px;
  height: 16px;
  color: #6366f1; /* AI图标使用紫色 */
}

.context-menu-item:hover svg {
  color: #4f46e5;
}

/* 保留原有的 overlay 样式，虽然 Radix UI 不需要，但保持兼容性 */
.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-nav {
    padding: 4px 8px;
  }

  .tab-navigation {
    gap: 1px;
  }

  .file-name {
    display: none;
  }

  .toolbar {
    padding: 6px 12px;
  }

  .toolbar-text-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}

/* 文件输入按钮样式 */
.toolbar-btn input[type="file"] {
  display: none;
}

.toolbar-btn label {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* 样式编辑面板 */
.style-panel {
  position: absolute;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.style-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  background: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.style-panel-content {
  padding: 8px 12px;
}

.style-group {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 0;
}

.style-group:last-child {
  margin-bottom: 0;
}

.style-toolbar {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 4px;
}

.style-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;

  background: white;
  cursor: pointer;
  transition: all 0.15s;
  font-size: 14px;
}

.style-btn:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.style-btn.active {
  background: #2563eb;
  border-color: #2563eb;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.style-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6b7280;
  white-space: nowrap;
}

.style-input {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 12px;
}

.style-color {
  width: 40px;
  height: 24px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
}

.style-color::-webkit-color-swatch-wrapper {
  padding: 0;
}

.style-color::-webkit-color-swatch {
  border: none;
  border-radius: 3px;
}

/* 节点加号按钮 */
.node-add-btn {
  position: absolute;
  width: 18px;
  height: 18px;
  border: 1px solid #d1d5db;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #9ca3af;
  transition: all 0.2s ease;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.node-add-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.node-add-btn:active {
  transform: scale(0.95);
  background: #eff6ff;
}

.AvatarRoot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  overflow: hidden;
  user-select: none;
  width: 45px;
  height: 45px;
  border-radius: 100%;
  background-color: var(--black-a3);
}

.AvatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.AvatarFallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: var(--violet-11);
  font-size: 15px;
  line-height: 1;
  font-weight: 500;
}
