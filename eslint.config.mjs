import prettierConfig from "eslint-config-prettier"
import eslint<PERSON>luginPrettier from "eslint-plugin-prettier"
import eslintPluginPrettierRecommended from "eslint-plugin-prettier/recommended"
import { defineConfig } from "eslint/config"
import globals from "globals"
import { createRequire } from "module"
import tseslint from "typescript-eslint"
const require = createRequire(import.meta.url)
export default defineConfig([
  // 忽略文件
  {
    ignores: [
      ".vscode/**",
      "node_modules/**",
      "dist/**",
      "**/*.min.js",
      "src/**/*.d.ts",
      "package.json",
      ".git/**",
      ".prettierrc",
      ".editorconfig"
    ]
  },

  // TypeScript 推荐规则
  ...tseslint.configs.recommended,

  // 自定义规则和环境声明
  {
    files: ["src/**/*.{js,ts,tsx}"],
    plugins: {
      prettier: eslintPluginPrettier,
      react: require("eslint-plugin-react")
    },
    rules: {
      "no-unused-vars": "off",
      "no-undef": "error",
      "prettier/prettier": "error",
      "react/react-in-jsx-scope": "off",
      "@typescript-eslint/no-explicit-any": "off"
    },
    languageOptions: {
      globals: {
        ...globals.browser
      }
    }
  },

  // 关闭 ESLint 和 Prettier 冲突的规则
  prettierConfig,
  eslintPluginPrettierRecommended
])
