import type { MindMapNode, NodeStyle } from "../types/mindmap"

// 默认节点样式 - 根据层级设置不同的字体大小
export const getDefaultNodeStyle = (level: number = 0): NodeStyle => {
  // 根据层级设置字体大小：根节点最大，每层递减2px
  // level 0: 18px, level 1: 16px, level 2: 14px, level 3+: 12px
  const fontSize = level === 0 ? 18 : Math.max(12, 18 - level * 2)

  return {
    fontFamily: "微软雅黑",
    fontSize,
    fontWeight: "normal",
    fontStyle: "normal",
    textDecoration: "none",
    color: "#000000",
    backgroundColor: "#ffffff",
    borderColor: "#d1d5db",
    borderWidth: 1
  }
}

// 获取节点默认名称
export const getDefaultNodeName = (parentLevel: number) => {
  switch (parentLevel) {
    case 0: // 根节点的子节点
      return "分支主题"
    case 1: // 分支主题的子节点
      return "子主题"
    case 2: // 子主题的子节点
      return "子主题"
    default:
      return "新节点"
  }
}

// 更新节点字体大小以匹配层级
export const updateNodeFontSizesByLevel = (
  nodes: Record<string, MindMapNode>
): Record<string, MindMapNode> => {
  const updatedNodes = { ...nodes }

  Object.values(updatedNodes).forEach((node) => {
    if (node.style) {
      // 根据层级设置字体大小：根节点最大，每层递减1.5px，最小不少于10px
      const fontSize = Math.max(10, 18 - node.level * 1.5)
      node.style.fontSize = fontSize
    }
  })

  return updatedNodes
}

// 估算节点宽度
const estimateNodeWidth = (node: MindMapNode): number => {
  // 尝试从DOM获取实际宽度
  const nodeElement = document.querySelector(
    `[data-node-id="${node.id}"]`
  ) as HTMLElement
  if (nodeElement && nodeElement.offsetWidth > 0) {
    return nodeElement.offsetWidth
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 处理多行文本，计算最宽的一行
  const lines = text.split("\n")
  let maxLineLength = 0
  lines.forEach((line) => {
    maxLineLength = Math.max(maxLineLength, line.length)
  })

  // 更精确的字符宽度计算
  const charWidth = fontSize * 0.65 // 稍微增加字符宽度估算
  const textWidth = maxLineLength * charWidth

  // 加上padding和边框 - 根据层级动态调整
  const padding = Math.max(20, 40 - node.level * 4) // 层级越深，padding越小，但不少于20
  const estimatedWidth = textWidth + padding

  // 动态调整最小和最大宽度
  const minWidth = Math.max(60, 120 - node.level * 10) // 层级越深，最小宽度越小
  const maxWidth = Math.max(400, 800 - node.level * 50) // 层级越深，最大宽度越小

  return Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
}

// 计算子树复杂度（用于智能分配左右节点）
const getSubtreeComplexity = (
  nodeId: string,
  nodes: Record<string, MindMapNode>
): number => {
  const node = nodes[nodeId]
  if (!node) return 1

  // 如果节点被折叠，只计算当前节点
  if (node.collapsed) return 1

  // 基础复杂度为1（当前节点）
  let complexity = 1

  // 递归计算所有子节点的复杂度
  if (node.children && node.children.length > 0) {
    node.children.forEach((childId) => {
      complexity += getSubtreeComplexity(childId, nodes)
    })
  }

  return complexity
}

// 估算节点高度
const estimateNodeHeight = (node: MindMapNode): number => {
  // 尝试从DOM获取实际高度
  const nodeElement = document.querySelector(
    `[data-node-id="${node.id}"]`
  ) as HTMLElement
  if (nodeElement && nodeElement.offsetHeight > 0) {
    return nodeElement.offsetHeight
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 计算行数
  const lines = text.split("\n")
  const lineCount = lines.length

  // 行高约为字体大小的1.2倍
  const lineHeight = fontSize * 1.2
  const textHeight = lineCount * lineHeight

  // 加上padding - 根据层级动态调整
  const padding = Math.max(12, 24 - node.level * 2) // 层级越深，padding越小
  const estimatedHeight = textHeight + padding

  // 设置最小高度 - 根据层级动态调整
  const minHeight = Math.max(24, 40 - node.level * 2) // 层级越深，最小高度越小

  return Math.max(estimatedHeight, minHeight)
}

// 解决子节点间的重叠问题
const resolveChildrenOverlap = (
  parentId: string,
  nodes: Record<string, MindMapNode>
) => {
  const parent = nodes[parentId]
  if (!parent || parent.children.length <= 1) return

  // 获取所有子节点的边界信息
  const childBounds = parent.children
    .map((childId) => ({
      childId,
      bounds: calculateSubtreeBounds(childId, nodes),
      node: nodes[childId]
    }))
    .filter((item) => item.node)

  // 按Y坐标排序
  childBounds.sort((a, b) => a.bounds.minY - b.bounds.minY)

  // 检测并解决重叠
  for (let i = 1; i < childBounds.length; i++) {
    const current = childBounds[i]
    const previous = childBounds[i - 1]

    // 计算最小间距
    const minSpacing = Math.max(30, 50 - current.node.level * 3)

    // 如果重叠，向下移动当前子树
    if (current.bounds.minY < previous.bounds.maxY + minSpacing) {
      const overlap = previous.bounds.maxY + minSpacing - current.bounds.minY
      moveSubtreeVertically(current.childId, overlap, nodes)

      // 重新计算边界
      current.bounds = calculateSubtreeBounds(current.childId, nodes)
    }
  }
}

// 垂直移动整个子树
const moveSubtreeVertically = (
  nodeId: string,
  deltaY: number,
  nodes: Record<string, MindMapNode>
) => {
  const node = nodes[nodeId]
  if (!node) return

  // 移动当前节点
  nodes[nodeId] = {
    ...node,
    y: node.y + deltaY
  }

  // 递归移动所有子节点
  node.children.forEach((childId) => {
    moveSubtreeVertically(childId, deltaY, nodes)
  })
}

// 计算子树的垂直边界（最小Y和最大Y）
const calculateSubtreeBounds = (
  nodeId: string,
  nodes: Record<string, MindMapNode>
): { minY: number; maxY: number; height: number } => {
  const node = nodes[nodeId]
  if (!node) return { minY: 0, maxY: 0, height: 0 }

  // 如果节点被折叠或没有子节点，只计算当前节点的边界
  if (node.collapsed || node.children.length === 0) {
    const nodeHeight = estimateNodeHeight(node)
    return {
      minY: node.y - nodeHeight / 2,
      maxY: node.y + nodeHeight / 2,
      height: nodeHeight
    }
  }

  // 递归计算所有子节点的边界
  let minY = node.y
  let maxY = node.y

  node.children.forEach((childId) => {
    const childBounds = calculateSubtreeBounds(childId, nodes)
    minY = Math.min(minY, childBounds.minY)
    maxY = Math.max(maxY, childBounds.maxY)
  })

  return {
    minY,
    maxY,
    height: maxY - minY
  }
}

// 重新计算所有节点位置 - 保持分支结构一致性
export const recalculateAllPositions = (
  mindMapNodes: Record<string, MindMapNode>
) => {
  const updatedNodes = { ...mindMapNodes }

  const layoutBranch = (parentId: string, level: number) => {
    const parent = updatedNodes[parentId]
    if (!parent || parent.children.length === 0) return

    // 如果父节点被折叠，跳过其子节点的布局计算
    if (parent.collapsed) return

    const childCount = parent.children.length
    const children = parent.children
      .map((id) => updatedNodes[id])
      .filter(Boolean)
    const parentWidth = estimateNodeWidth(parent)

    // 根据层级动态调整基础距离，层级越深距离越小
    const levelMultiplier = Math.max(0.6, 1 - level * 0.1) // 每层减少10%，最小60%
    const baseDistance = Math.max(
      180,
      (parentWidth / 2 + 120) * levelMultiplier
    )

    if (parent.id === "root") {
      // 根节点的子节点：左右分布，优化对称性
      // 智能分配策略：考虑节点复杂度和视觉平衡
      let leftSideChildren: MindMapNode[] = []
      let rightSideChildren: MindMapNode[] = []

      if (childCount === 1) {
        // 单个子节点放在右侧（传统思维导图习惯）
        rightSideChildren = children
      } else if (childCount === 2) {
        // 两个子节点，右侧先放一个，左侧放一个
        rightSideChildren = [children[0]]
        leftSideChildren = [children[1]]
      } else {
        // 多个子节点：从右侧开始分配，右侧稍微多一个
        const rightSideCount = Math.ceil(childCount / 2)
        rightSideChildren = children.slice(0, rightSideCount)
        leftSideChildren = children.slice(rightSideCount)
      }

      // 计算统一的间距，确保左右两侧一致
      const baseSpacing = Math.max(120, 200 - level * 15) // 增加基础间距，确保足够的空间

      // 处理左侧节点
      if (leftSideChildren.length > 0) {
        const leftTotalHeight = (leftSideChildren.length - 1) * baseSpacing
        const leftStartY = parent.y - leftTotalHeight / 2

        leftSideChildren.forEach((child, index) => {
          const childWidth = estimateNodeWidth(child)
          const distance = Math.max(
            baseDistance,
            parentWidth / 2 + childWidth / 2 + 100
          )

          updatedNodes[child.id] = {
            ...child,
            x: parent.x - distance,
            y: leftStartY + index * baseSpacing
          }
        })
      }

      // 处理右侧节点（使用相同的间距）
      if (rightSideChildren.length > 0) {
        const rightTotalHeight = (rightSideChildren.length - 1) * baseSpacing
        const rightStartY = parent.y - rightTotalHeight / 2

        rightSideChildren.forEach((child, index) => {
          const childWidth = estimateNodeWidth(child)
          const distance = Math.max(
            baseDistance,
            parentWidth / 2 + childWidth / 2 + 100
          )

          updatedNodes[child.id] = {
            ...child,
            x: parent.x + distance,
            y: rightStartY + index * baseSpacing
          }
        })
      }
    } else {
      // 分支节点的子节点：保持一致的内部结构
      const isParentOnRight = parent.x > updatedNodes["root"].x
      // 根据层级动态调整间距：层级越深，间距越小，但要考虑子树高度
      const baseSpacing = Math.max(80, 140 - level * 12) // 每层减少12px，但保持更大的基础间距

      // 计算子节点的平均子树高度，用于调整间距
      const avgSubtreeHeight =
        children.reduce((sum, child) => {
          const childBounds = calculateSubtreeBounds(child.id, updatedNodes)
          return sum + childBounds.height
        }, 0) / children.length

      // 根据子树复杂度调整间距
      const complexityMultiplier = Math.max(1, avgSubtreeHeight / 100)
      const enhancedSpacing = Math.max(
        baseSpacing,
        baseSpacing * complexityMultiplier
      )
      const totalHeight = (childCount - 1) * enhancedSpacing
      const startY = parent.y - totalHeight / 2

      children.forEach((child, index) => {
        const childWidth = estimateNodeWidth(child)
        const distance = Math.max(
          baseDistance,
          parentWidth / 2 + childWidth / 2 + 100
        )
        const newX = isParentOnRight ? parent.x + distance : parent.x - distance
        const newY = startY + index * enhancedSpacing

        updatedNodes[child.id] = {
          ...child,
          x: newX,
          y: newY
        }
      })
    }

    // 递归处理子节点
    parent.children.forEach((childId) => {
      layoutBranch(childId, level + 1)
    })

    // 在当前层级检测和解决子分支间的重叠
    if (parent.children.length > 1 && parent.id !== "root") {
      resolveChildrenOverlap(parent.id, updatedNodes)
    }
  }

  // 第一步：标准布局
  layoutBranch("root", 0)

  // 第二步：检测和解决分支间的重叠
  const rootNode = updatedNodes["root"]
  if (!rootNode) return updatedNodes

  const rightBranches: string[] = []
  const leftBranches: string[] = []

  // 分类左右分支
  rootNode.children.forEach((childId) => {
    const child = updatedNodes[childId]
    if (child) {
      if (child.x > rootNode.x) {
        rightBranches.push(childId)
      } else {
        leftBranches.push(childId)
      }
    }
  })

  // 智能调整分支位置，保持对称性
  const adjustBranchPositionsSymmetrically = (branchIds: string[]) => {
    if (branchIds.length <= 1) return

    const branchBounds = branchIds.map((branchId) => ({
      branchId,
      bounds: calculateSubtreeBounds(branchId, updatedNodes)
    }))

    // 按当前Y位置排序
    branchBounds.sort((a, b) => a.bounds.minY - b.bounds.minY)

    // 检查是否有重叠
    let hasOverlap = false
    const minSpacing = 50 // 固定最小间距，确保一致性

    for (let i = 1; i < branchBounds.length; i++) {
      const currentBranch = branchBounds[i]
      const prevBranch = branchBounds[i - 1]

      if (currentBranch.bounds.minY < prevBranch.bounds.maxY + minSpacing) {
        hasOverlap = true
        break
      }
    }

    // 如果有重叠，重新分布所有分支
    if (hasOverlap) {
      const totalHeight = branchBounds.reduce(
        (sum, branch) => sum + branch.bounds.height,
        0
      )
      const totalSpacing = (branchBounds.length - 1) * minSpacing
      const requiredHeight = totalHeight + totalSpacing

      // 计算新的起始位置（以根节点为中心）
      const rootNode = updatedNodes["root"]
      const startY = rootNode.y - requiredHeight / 2

      let currentY = startY
      branchBounds.forEach((branch) => {
        const branchNode = updatedNodes[branch.branchId]
        const heightOffset = branch.bounds.height / 2
        const newY = currentY + heightOffset

        // 移动整个分支
        const deltaY = newY - branchNode.y
        moveSubtreeVertically(branch.branchId, deltaY, updatedNodes)

        currentY += branch.bounds.height + minSpacing
      })
    }
  }

  // 智能处理根节点的直接子节点
  const adjustRootChildrenSymmetrically = (
    leftBranches: string[],
    rightBranches: string[],
    nodes: Record<string, MindMapNode>
  ) => {
    const rootNode = nodes["root"]
    if (!rootNode) return

    // 只处理根节点的直接子节点，其他层级使用简单的重叠检测
    const rootDirectChildren = rootNode.children || []
    const leftRootChildren = leftBranches.filter((id) =>
      rootDirectChildren.includes(id)
    )
    const rightRootChildren = rightBranches.filter((id) =>
      rootDirectChildren.includes(id)
    )

    // 只有当左右两侧节点数量完全相等时才进行对称处理
    if (
      leftRootChildren.length === rightRootChildren.length &&
      leftRootChildren.length > 0
    ) {
      const leftHeight = calculateVisibleSideHeight(leftRootChildren, nodes)
      const rightHeight = calculateVisibleSideHeight(rightRootChildren, nodes)

      // 使用较大的高度作为基准，确保根节点子节点对称
      const maxHeight = Math.max(leftHeight, rightHeight, 200) // 最小高度200px

      redistributeRootChildren(leftRootChildren, maxHeight, nodes)
      redistributeRootChildren(rightRootChildren, maxHeight, nodes)
    } else {
      // 数量不等时，各自独立布局
      // 左侧单个节点时，与根节点水平对齐
      if (leftRootChildren.length === 1) {
        const leftNode = nodes[leftRootChildren[0]]
        if (leftNode) {
          const deltaY = rootNode.y - leftNode.y
          moveSubtreeVertically(leftRootChildren[0], deltaY, nodes)
        }
      } else if (leftRootChildren.length > 1) {
        // 多个左侧节点时，正常分布
        adjustBranchPositionsSimple(leftRootChildren, nodes)
      }

      // 右侧节点正常分布
      if (rightRootChildren.length > 1) {
        adjustBranchPositionsSimple(rightRootChildren, nodes)
      } else if (rightRootChildren.length === 1) {
        // 右侧单个节点时，也与根节点水平对齐
        const rightNode = nodes[rightRootChildren[0]]
        if (rightNode) {
          const deltaY = rootNode.y - rightNode.y
          moveSubtreeVertically(rightRootChildren[0], deltaY, nodes)
        }
      }
    }

    // 对其他层级的分支进行简单的重叠检测
    const otherLeftBranches = leftBranches.filter(
      (id) => !rootDirectChildren.includes(id)
    )
    const otherRightBranches = rightBranches.filter(
      (id) => !rootDirectChildren.includes(id)
    )

    adjustBranchPositionsSimple(otherLeftBranches, nodes)
    adjustBranchPositionsSimple(otherRightBranches, nodes)
  }

  // 计算可见节点的高度（考虑折叠状态）
  const calculateVisibleSideHeight = (
    branchIds: string[],
    nodes: Record<string, MindMapNode>
  ) => {
    if (branchIds.length === 0) return 0

    const bounds = branchIds.map((id) => {
      const node = nodes[id]
      if (node && node.collapsed) {
        // 如果节点折叠，只计算节点本身的高度
        return { height: 60 } // 单个节点的估计高度
      } else {
        return calculateSubtreeBounds(id, nodes)
      }
    })

    const totalContentHeight = bounds.reduce(
      (sum, bound) => sum + bound.height,
      0
    )
    const spacingHeight = (branchIds.length - 1) * 80 // 根节点子节点间距稍大
    return totalContentHeight + spacingHeight
  }

  // 重新分布根节点的直接子节点
  const redistributeRootChildren = (
    branchIds: string[],
    totalHeight: number,
    nodes: Record<string, MindMapNode>
  ) => {
    if (branchIds.length === 0) return

    const rootNode = nodes["root"]
    const startY = rootNode.y - totalHeight / 2

    // 按当前Y位置排序
    const sortedBranches = branchIds
      .map((id) => ({ id, node: nodes[id] }))
      .filter((item) => item.node)
      .sort((a, b) => a.node.y - b.node.y)

    let currentY = startY

    sortedBranches.forEach((branch, index) => {
      const bounds = branch.node.collapsed
        ? { height: 60 }
        : calculateSubtreeBounds(branch.id, nodes)
      const branchCenterY = currentY + bounds.height / 2

      // 移动分支到新位置
      const deltaY = branchCenterY - branch.node.y
      moveSubtreeVertically(branch.id, deltaY, nodes)

      currentY += bounds.height + (index < sortedBranches.length - 1 ? 80 : 0)
    })
  }

  // 简单的重叠检测（用于非根节点的子节点）
  const adjustBranchPositionsSimple = (
    branchIds: string[],
    nodes: Record<string, MindMapNode>
  ) => {
    if (branchIds.length <= 1) return

    const branchBounds = branchIds.map((branchId) => ({
      branchId,
      bounds: calculateSubtreeBounds(branchId, nodes)
    }))

    // 按当前Y位置排序
    branchBounds.sort((a, b) => a.bounds.minY - b.bounds.minY)

    // 检查并调整重叠
    for (let i = 1; i < branchBounds.length; i++) {
      const currentBranch = branchBounds[i]
      const prevBranch = branchBounds[i - 1]
      const minSpacing = 40 // 非根节点的最小间距

      // 如果当前分支与前一个分支重叠
      if (currentBranch.bounds.minY < prevBranch.bounds.maxY + minSpacing) {
        const overlap =
          prevBranch.bounds.maxY + minSpacing - currentBranch.bounds.minY

        // 向下移动当前分支及其所有子树
        moveSubtreeVertically(currentBranch.branchId, overlap, nodes)

        // 重新计算边界
        currentBranch.bounds = calculateSubtreeBounds(
          currentBranch.branchId,
          nodes
        )
      }
    }
  }

  // 使用新的根节点对称重叠检测
  adjustRootChildrenSymmetrically(leftBranches, rightBranches, updatedNodes)

  return updatedNodes
}

const performGlobalOverlapFix = (nodes: Record<string, MindMapNode>) => {
  // 递归检查每个节点的子节点是否有重叠
  const checkNodeChildren = (nodeId: string) => {
    const node = nodes[nodeId]
    if (!node || node.children.length <= 1) return

    // 解决当前节点的子节点重叠
    resolveChildrenOverlap(nodeId, nodes)

    // 递归检查所有子节点
    node.children.forEach((childId) => {
      checkNodeChildren(childId)
    })
  }

  // 从根节点开始递归检查
  checkNodeChildren("root")
}

// 导出思维导图为JSON文件
export const exportMindMap = (mindMapNodes: Record<string, MindMapNode>) => {
  try {
    const dataStr = JSON.stringify(mindMapNodes, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `mindmap-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error("导出思维导图失败:", error)
    alert("导出失败，请重试")
  }
}
