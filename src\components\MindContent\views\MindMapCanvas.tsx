import React, { useEffect, useState } from "react"
import type {
  MindMapNode as MindMapNodeType,
  NodeStyle,
  ViewOffset
} from "../../../types/mindmap"
import { MindMapNodeWithContextMenu } from "./MindMapNodeWithContextMenu"

interface MindMapCanvasProps {
  mindMapNodes: Record<string, MindMapNodeType>
  selectedNodeId: string | null
  isEditingNode: string | null
  editingText: string
  editingStyle: NodeStyle
  showStylePanel: string | null
  showAddButton: string | null
  isDragging: boolean
  viewOffset: ViewOffset
  onNodeSelect: (nodeId: string) => void
  onNodeDoubleClick: (nodeId: string) => void
  onNodeContextMenu: (e: React.MouseEvent, nodeId: string) => void
  onNodeMouseDown: (e: React.MouseEvent) => void
  onEditingTextChange: (text: string) => void
  onSaveEdit: () => void
  onCancelEdit: () => void
  onStyleChange: (nodeId: string, style: NodeStyle) => void
  onAddChildNode: (nodeId: string) => void
  onCanvasClick: () => void
  onMouseMove: (e: React.MouseEvent) => void
  onMouseUp: () => void
  onMouseLeave: () => void
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onToggleUnderline?: () => void
  onColorChange?: (color: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
  onToggleCollapse?: (nodeId: string) => void
  // Context menu actions
  onAddSiblingNode: (nodeId: string) => void
  onAddParentNode: (nodeId: string) => void
  onDeleteNode: (nodeId: string) => void
  onAIGenerate?: (nodeId: string, content: string) => void
}

export const MindMapCanvas = ({
  mindMapNodes,
  selectedNodeId,
  isEditingNode,
  editingText,
  editingStyle,
  showStylePanel,
  showAddButton,
  isDragging,
  viewOffset,
  onNodeSelect,
  onNodeDoubleClick,
  onNodeContextMenu,
  onNodeMouseDown,
  onEditingTextChange,
  onSaveEdit,
  onCancelEdit,
  onStyleChange,
  onAddChildNode,
  onCanvasClick,
  onMouseMove,
  onMouseUp,
  onMouseLeave,
  onToggleBold,
  onToggleItalic,
  onToggleUnderline,
  onColorChange,
  onFontSizeChange,
  onTextAlignChange,
  onToggleCollapse,
  onAddSiblingNode,
  onAddParentNode,
  onDeleteNode,
  onAIGenerate
}: MindMapCanvasProps) => {
  const handleNodeSelect = (nodeId: string) => {
    onNodeSelect(nodeId)
  }

  const handleNodeDoubleClick = (nodeId: string) => {
    onNodeDoubleClick(nodeId)
  }

  const handleNodeContextMenu = (e: React.MouseEvent, nodeId: string) => {
    onNodeContextMenu(e, nodeId)
  }

  const handleStyleChange = (nodeId: string, style: NodeStyle) => {
    onStyleChange(nodeId, style)
  }

  const handleAddChild = (nodeId: string) => {
    onAddChildNode(nodeId)
  }

  const handleToggleCollapse = (nodeId: string) => {
    onToggleCollapse?.(nodeId)
  }

  // 计算可见节点（被折叠的子节点不显示）
  const getVisibleNodes = () => {
    const visibleNodes: Record<string, MindMapNodeType> = {}

    // 递归遍历，从根节点开始
    const addVisibleNode = (nodeId: string) => {
      const node = mindMapNodes[nodeId]
      if (!node) return

      // 添加当前节点
      visibleNodes[nodeId] = node

      // 如果节点没有被折叠，添加其子节点
      if (!node.collapsed && node.children.length > 0) {
        node.children.forEach((childId) => addVisibleNode(childId))
      }
    }

    addVisibleNode("root")
    return visibleNodes
  }

  const visibleNodes = getVisibleNodes()

  // 强制重新渲染的key
  const [renderKey, setRenderKey] = useState(0)

  useEffect(() => {
    setRenderKey((prev) => prev + 1)
  }, [mindMapNodes])

  useEffect(() => {
    setRenderKey((prev) => prev + 1)
  }, [selectedNodeId])

  useEffect(() => {
    setRenderKey((prev) => prev + 1)
  }, [isEditingNode])

  useEffect(() => {
    setRenderKey((prev) => prev + 1)
  }, [showStylePanel])

  return (
    <div
      className="flex-1 mindmap-container"
      onClick={(e) => {
        // 只在点击空白区域时触发
        if (e.target === e.currentTarget) {
          onCanvasClick()
        }
      }}
      onMouseMove={onMouseMove}
      onMouseUp={onMouseUp}
      onMouseLeave={onMouseLeave}
    >
      {/* SVG for drawing lines between nodes */}
      <svg key={renderKey} className="mindmap-svg">
        {/* 渲染节点间的连接线 */}
        {Object.values(visibleNodes).map((node) => {
          if (!node.parentId) return null

          const parent = visibleNodes[node.parentId]

          // 获取节点实际宽度和高度的函数
          const getActualNodeDimensions = (targetNode: MindMapNodeType) => {
            // 尝试从DOM获取实际尺寸
            const nodeElement = document.querySelector(
              `[data-node-id="${targetNode.id}"]`
            ) as HTMLElement
            if (
              nodeElement &&
              nodeElement.offsetWidth > 0 &&
              nodeElement.offsetHeight > 0
            ) {
              return {
                width: nodeElement.offsetWidth,
                height: nodeElement.offsetHeight
              }
            }

            // 如果无法获取DOM元素，使用更精确的估算
            const fontSize = targetNode.style?.fontSize || 14
            const text = targetNode.text || "新节点"

            // 处理多行文本，计算最宽的一行和行数
            const lines = text.split("\n")
            let maxLineLength = 0
            lines.forEach((line) => {
              maxLineLength = Math.max(maxLineLength, line.length)
            })

            // 更精确的字符宽度计算
            const charWidth = fontSize * 0.65
            const textWidth = maxLineLength * charWidth
            const padding =
              targetNode.level === 0 ? 40 : targetNode.level === 1 ? 32 : 24
            const estimatedWidth = textWidth + padding
            const minWidth =
              targetNode.level === 0 ? 120 : targetNode.level === 1 ? 100 : 80
            const maxWidth = targetNode.level === 0 ? 600 : 500

            const width = Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
            const lineHeight = fontSize * 1.4
            const height = lines.length * lineHeight + padding * 0.8

            return { width, height }
          }

          const nodeDimensions = getActualNodeDimensions(node)
          const nodeWidth = nodeDimensions.width
          const parentDimensions = getActualNodeDimensions(parent)
          const parentWidth = parentDimensions.width

          // 判断子节点在父节点的哪一侧
          const isRight = node.x > parent.x

          // 起点：父节点的边缘（而不是中心）
          const x1 = isRight
            ? parent.x + parentWidth / 2 + viewOffset.x // 子节点在右边 → 从父节点右边缘开始
            : parent.x - parentWidth / 2 + viewOffset.x // 子节点在左边 → 从父节点左边缘开始
          const y1 = parent.y + viewOffset.y

          // 终点：子节点靠近父节点的边缘
          const x2 = isRight
            ? node.x - nodeWidth / 2 + viewOffset.x // 子节点在右边 → 连接到其左边缘
            : node.x + nodeWidth / 2 + viewOffset.x // 子节点在左边 → 连接到其右边缘
          const y2 = node.y + viewOffset.y

          // 水平距离（用于控制曲率）
          const dx = x2 - x1
          const minControl = 40 // 最小控制距离，防止线条反向
          const controlDist = Math.max(Math.abs(dx) * 0.5, minControl)

          // 控制点坐标
          const cx1 = x1 + (isRight ? controlDist : -controlDist)
          const cy1 = y1
          const cx2 = x2 - (isRight ? controlDist : -controlDist)
          const cy2 = y2

          // 贝塞尔曲线路径
          const pathData = `M ${x1},${y1} C ${cx1},${cy1} ${cx2},${cy2} ${x2},${y2}`

          return (
            <path
              key={`line-${node.id}`}
              className="mindmap-line"
              d={pathData}
              fill="none"
            />
          )
        })}
      </svg>

      <div
        className="mindmap-nodes-container"
        style={{
          transform: `translate(${viewOffset.x}px, ${viewOffset.y}px)`,
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%"
        }}
      >
        {Object.values(visibleNodes).map((node) => {
          return (
            <MindMapNodeWithContextMenu
              key={node.id}
              node={node}
              rootNode={mindMapNodes.root}
              isSelected={selectedNodeId === node.id}
              isEditing={isEditingNode === node.id}
              editingText={editingText}
              editingStyle={editingStyle}
              showStylePanel={showStylePanel === node.id}
              showAddButton={showAddButton === node.id}
              isDragging={isDragging}
              mindMapNodes={mindMapNodes}
              onSelect={() => handleNodeSelect(node.id)} // 单击选中节点（用于header工具栏）
              onDoubleClick={() => handleNodeDoubleClick(node.id)} // 双击进入编辑模式（显示节点工具栏）
              onContextMenu={(e) => handleNodeContextMenu(e, node.id)}
              onMouseDown={onNodeMouseDown}
              onEditingTextChange={onEditingTextChange}
              onSaveEdit={onSaveEdit}
              onCancelEdit={onCancelEdit}
              onStyleChange={(style) => handleStyleChange(node.id, style)}
              onAddChild={() => handleAddChild(node.id)}
              onToggleBold={onToggleBold}
              onToggleItalic={onToggleItalic}
              onToggleUnderline={onToggleUnderline}
              onColorChange={onColorChange}
              onFontSizeChange={onFontSizeChange}
              onTextAlignChange={onTextAlignChange}
              onToggleCollapse={() => handleToggleCollapse(node.id)}
              onAddChildNode={onAddChildNode}
              onAddSiblingNode={onAddSiblingNode}
              onAddParentNode={onAddParentNode}
              onDeleteNode={onDeleteNode}
              onAIGenerate={onAIGenerate}
            />
          )
        })}
      </div>
    </div>
  )
}
